<!DOCTYPE html>
<html>

<head>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@annotorious/annotorious@latest/dist/annotorious.css">
    <script src="https://cdn.jsdelivr.net/npm/@annotorious/annotorious@latest/dist/annotorious.js"></script>
</head>

<body>
    <div style="width: 500px;">

        <img id="my-image" src="https://images.pexels.com/photos/31241763/pexels-photo-31241763.jpeg"
            alt="Annotatable image" width="500px" />
    </div>
    <script>
        window.onload = function () {
            // console.log(Annotorious.createImageAnnotator);

            const anno = Annotorious.createImageAnnotator('my-image', {
                // widgets: [
                //     // Enables the default COMMENT input box
                //     { widget: 'COMMENT' }
                // ],
                // allowEmpty: false,
                motivation: 'commenting',
                purpose: 'commenting',
                // drawingEnabled: true,
                // style: {
                //     fill: '#ff0000',
                //     fillOpacity: 0.15
                // }
                drawingEnabled: true,
                // drawingMode: "click",
                // userSelectAction: 'EDIT'
            });
            // anno.destroy();
            // anno.setStyle((annotation, state) => {
            //     console.log(annotation, state);

            //     // if (state.selected) {
            //     //     return {
            //     //         fill: '#ff0000',
            //     //         fillOpacity: 0.3,
            //     //         stroke: '#ff0000',
            //     //         strokeOpacity: 1
            //     //     };
            //     // }

            //     // Default style for non-selected annotations
            //     return {
            //         fill: '#00ff00',
            //         fillOpacity: 0.2,
            //         stroke: '#00ff00',
            //         strokeOpacity: 1
            //     };
            // });
            anno.setUser({
                id: 'user-123',
                name: 'Sourabh Jingar' // Replace with dynamic user name
            });

            anno.on('createAnnotation', annotation => {
                console.log('Created annotation:', annotation);
                console.log('anno', anno.getAnnotations());

                anno.getAnnotations().forEach(ann => {
                    const comment = annotation.body?.find(b => b.purpose === 'commenting')?.value;
                    console.log(comment);
                    console.log(ann);

                });
            });
        };
    </script>
</body>

</html>