<!DOCTYPE html>
<html>

<head>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@annotorious/annotorious@latest/dist/annotorious.css">
    <script src="https://cdn.jsdelivr.net/npm/@annotorious/annotorious@latest/dist/annotorious.js"></script>
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <style>
        .comment-highlighted {
            border: 2px solid #0d6efd !important;
        }

        .comment-edit-input {
            resize: vertical;
            min-height: 60px;
        }
    </style>
</head>

<body>
    <div class="container-fluid p-3">
        <div class="row">
            <div class="col-md-8">
                <img id="my-image" src="https://images.pexels.com/photos/31241763/pexels-photo-31241763.jpeg"
                    alt="Annotatable image" class="img-fluid" />
            </div>
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">Comments</h5>
                    </div>
                    <div class="card-body">
                        <div id="comments-list" style="max-height: 50vh;overflow: auto;"></div>
                        <button class="btn btn-danger btn-sm mt-2" onclick="clearAllComments()">Clear All
                            Comments</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script>
        // Comment management functions
        function saveCommentToStorage(comment) {
            let comments = JSON.parse(localStorage.getItem('annotorious-comments') || '[]');
            comment.id = Date.now().toString(); // Simple ID generation
            comment.timestamp = new Date().toLocaleString();
            comments.push(comment);
            localStorage.setItem('annotorious-comments', JSON.stringify(comments));
            displayComments();
        }
        console.log(JSON.parse(localStorage.getItem('annotorious-comments')));

        function saveAnnotationToStorage(annotation) {
            let annotations = JSON.parse(localStorage.getItem('annotorious-annotations') || '[]');
            // Remove existing annotation with same ID if it exists
            annotations = annotations.filter(a => a.id !== annotation.id);
            annotations.push(annotation);
            localStorage.setItem('annotorious-annotations', JSON.stringify(annotations));
        }

        function loadAnnotationsFromStorage() {
            return JSON.parse(localStorage.getItem('annotorious-annotations') || '[]');
        }

        function removeAnnotationFromStorage(annotationId) {
            let annotations = loadAnnotationsFromStorage();
            annotations = annotations.filter(a => a.id !== annotationId);
            localStorage.setItem('annotorious-annotations', JSON.stringify(annotations));
        }

        function loadCommentsFromStorage() {
            return JSON.parse(localStorage.getItem('annotorious-comments') || '[]');
        }



        // jQuery function to highlight comment
        function highlightComment(annotationId) {
            // Remove previous highlights
            $('.comment-item').removeClass('comment-highlighted highlight-pulse');

            // Find and highlight the comment
            const $commentItem = $(`.comment-item[data-annotation-id="${annotationId}"]`);
            if ($commentItem.length > 0) {
                $commentItem.addClass('comment-highlighted highlight-pulse');

                // Scroll to the comment if it's not visible
                $commentItem[0].scrollIntoView({
                    behavior: 'smooth',
                    block: 'center'
                });

                // Remove highlight after 3 seconds
                // setTimeout(() => {
                //     $commentItem.removeClass('comment-highlighted highlight-pulse');
                // }, 3000);
            }
        }

        // jQuery function to remove all highlights
        function removeAllHighlights() {
            $('.comment-item').removeClass('comment-highlighted highlight-pulse');
        }

        // Function to highlight annotation on the image
        function highlightAnnotationOnImage(annotationId) {
            if (!window.annotoriusInstance) return;

            const annotation = window.annotoriusInstance.getAnnotations().find(a => a.id === annotationId);
            if (annotation) {
                // First, clear any existing selections
                window.annotoriusInstance.clearSelection();

                // Select the annotation to highlight it
                window.annotoriusInstance.setSelected(annotation);

                // Add custom styling for enhanced visibility
                window.annotoriusInstance.setStyle((ann, state) => {
                    if (ann.id === annotationId && state.selected) {
                        return {
                            fill: '#ff6b6b',
                            fillOpacity: 0.3,
                            stroke: '#ff6b6b',
                            strokeWidth: 3,
                            strokeOpacity: 1
                        };
                    }
                    // Default style for other annotations
                    return {
                        fill: '#007bff',
                        fillOpacity: 0.2,
                        stroke: '#007bff',
                        strokeWidth: 2,
                        strokeOpacity: 0.8
                    };
                });

                console.log('Annotation highlighted on image:', annotationId);
            }
        }

        function displayComments() {
            const commentsList = document.getElementById('comments-list');
            let comments = loadCommentsFromStorage();

            if (comments.length === 0) {
                commentsList.innerHTML = '<div class="no-comments">No comments yet. Create an annotation to add a comment!</div>';
                return;
            }

            commentsList.innerHTML = comments.map(comment => `
                <div class="comment-item" data-comment-id="${comment.id}" data-annotation-id="${comment.annotationId}">
                    <div class="comment-text" id="comment-text-${comment.id}">${comment.text || 'No comment text'}</div>
                    <div class="comment-edit-container" id="edit-container-${comment.id}" style="display: none;">
                        <textarea class="comment-edit-input" id="edit-input-${comment.id}">${comment.text || ''}</textarea>
                    </div>
                    <div class="comment-meta">
                        <strong>By:</strong> ${comment.user || 'Anonymous'}<br>
                        <strong>Date:</strong> ${comment.timestamp}<br>
                 
                    </div>
                    <div class="comment-actions">
                        <button class="edit-btn" onclick="editComment('${comment.id}')">Edit</button>
                        <button class="delete-btn" onclick="deleteComment('${comment.id}', '${comment.annotationId}')">Delete</button>
                        <button class="save-btn" id="save-btn-${comment.id}" style="display: none;" onclick="saveComment('${comment.id}', '${comment.annotationId}')">Save</button>
                        <button class="cancel-btn" id="cancel-btn-${comment.id}" style="display: none;" onclick="cancelEdit('${comment.id}')">Cancel</button>
                    </div>
                </div>
            `).join('');
        }

        function editComment(commentId) {
            const $textDiv = $(`#comment-text-${commentId}`);
            const $editContainer = $(`#edit-container-${commentId}`);
            const $editBtn = $(`.edit-btn[onclick="editComment('${commentId}')"]`);
            const $deleteBtn = $(`.delete-btn[onclick*="deleteComment('${commentId}"]`);
            const $saveBtn = $(`#save-btn-${commentId}`);
            const $cancelBtn = $(`#cancel-btn-${commentId}`);

            $textDiv.hide();
            $editContainer.show();
            $editBtn.hide();
            $deleteBtn.hide();
            $saveBtn.show();
            $cancelBtn.show();
        }

        function cancelEdit(commentId) {
            const $textDiv = $(`#comment-text-${commentId}`);
            const $editContainer = $(`#edit-container-${commentId}`);
            const $editBtn = $(`.edit-btn[onclick="editComment('${commentId}')"]`);
            const $deleteBtn = $(`.delete-btn[onclick*="deleteComment('${commentId}"]`);
            const $saveBtn = $(`#save-btn-${commentId}`);
            const $cancelBtn = $(`#cancel-btn-${commentId}`);

            $textDiv.show();
            $editContainer.hide();
            $editBtn.show();
            $deleteBtn.show();
            $saveBtn.hide();
            $cancelBtn.hide();
        }

        function saveComment(commentId, annotationId) {
            const newText = $(`#edit-input-${commentId}`).val().trim();

            if (!newText) {
                alert('Comment cannot be empty!');
                return;
            }

            // Update comment in localStorage
            let comments = loadCommentsFromStorage();
            const commentIndex = comments.findIndex(c => c.id === commentId);

            if (commentIndex !== -1) {
                comments[commentIndex].text = newText;
                comments[commentIndex].timestamp = new Date().toLocaleString();
                localStorage.setItem('annotorious-comments', JSON.stringify(comments));

                // Update the annotation in Annotorious
                updateAnnotationComment(annotationId, newText);

                displayComments();
            }
        }

        function deleteComment(commentId, annotationId) {
            if (confirm('Are you sure you want to delete this comment?')) {
                // Remove comment from localStorage
                let comments = loadCommentsFromStorage();
                comments = comments.filter(c => c.id !== commentId);
                localStorage.setItem('annotorious-comments', JSON.stringify(comments));

                // Delete the annotation from Annotorious
                deleteAnnotationFromMap(annotationId);

                displayComments();
            }
        }

        function clearAllComments() {
            if (confirm('Are you sure you want to clear all comments? This action cannot be undone.')) {
                localStorage.removeItem('annotorious-comments');
                localStorage.removeItem('annotorious-annotations');
                // Also clear all annotations from the map
                if (window.annotoriusInstance) {
                    const annotations = window.annotoriusInstance.getAnnotations();
                    annotations.forEach(annotation => {
                        window.annotoriusInstance.removeAnnotation(annotation);
                    });
                }
                displayComments();
            }
        }

        // Functions to interact with Annotorious annotations
        function updateAnnotationComment(annotationId, newText) {
            if (!window.annotoriusInstance) return;

            const annotation = window.annotoriusInstance.getAnnotations().find(a => a.id === annotationId);
            if (annotation) {
                // Create updated annotation with new comment
                const updatedAnnotation = {
                    ...annotation,
                    bodies: annotation.bodies.map(body => {
                        if (body.purpose === 'commenting') {
                            return {
                                ...body,
                                value: newText,
                                modified: new Date().toISOString()
                            };
                        }
                        return body;
                    })
                };

                // Update the annotation in Annotorious
                window.annotoriusInstance.updateAnnotation(updatedAnnotation, annotation);
            }
        }

        function deleteAnnotationFromMap(annotationId) {
            if (!window.annotoriusInstance) return;

            const annotation = window.annotoriusInstance.getAnnotations().find(a => a.id === annotationId);
            if (annotation) {
                window.annotoriusInstance.removeAnnotation(annotation);
                removeAnnotationFromStorage(annotationId);
            }
        }

        function loadExistingAnnotations() {
            if (!window.annotoriusInstance) return;

            const savedAnnotations = loadAnnotationsFromStorage();
            console.log('Loading saved annotations:', savedAnnotations);

            savedAnnotations.forEach(annotation => {
                try {
                    // Add the annotation to the map
                    window.annotoriusInstance.addAnnotation(annotation);
                    // console.log('Restored annotation:', annotation.id);
                } catch (error) {
                    console.error('Error restoring annotation:', error, annotation);
                }
            });
        }

        // jQuery document ready function
        $(document).ready(function () {
            // Initialize comments display
            displayComments();

            const anno = Annotorious.createImageAnnotator('my-image', {
                widgets: [
                    // Enables the default COMMENT input box
                    { widget: 'COMMENT' }
                ],
                drawingEnabled: true
            });

            // Store the annotorious instance globally for access from other functions
            window.annotoriusInstance = anno;

            anno.setUser({
                id: 'user-123',
                name: 'sourabh'
            });

            // Load existing annotations from localStorage after a short delay
            setTimeout(() => {
                loadExistingAnnotations();
            }, 100);

            // Event: When annotation is created
            anno.on('createAnnotation', annotation => {
                console.log('Created annotation:', annotation);

                // Save the annotation to localStorage
                saveAnnotationToStorage(annotation);

                // Extract comment data and save to localStorage
                const commentText = annotation.bodies?.find(b => b.purpose === 'commenting')?.value || 'No comment';
                const commentData = {
                    text: commentText,
                    user: 'Anonymous User',
                    annotation: annotation,
                    annotationId: annotation.id

                };

                saveCommentToStorage(commentData);
            });

            // Event: When annotation is updated
            anno.on('updateAnnotation', (annotation, previous) => {
                console.log('Annotation updated:', annotation, previous);

                // Update the annotation in localStorage
                saveAnnotationToStorage(annotation);

                // Update comment in localStorage
                let comments = loadCommentsFromStorage();
                const commentIndex = comments.findIndex(c => c.annotationId === annotation.id);

                if (commentIndex !== -1) {
                    const commentText = annotation.bodies?.find(b => b.purpose === 'commenting')?.value || 'No comment';
                    comments[commentIndex].text = commentText;
                    comments[commentIndex].timestamp = new Date().toLocaleString();
                    localStorage.setItem('annotorious-comments', JSON.stringify(comments));
                    displayComments();
                }
            });

            // Event: When annotation is deleted
            anno.on('deleteAnnotation', annotation => {
                console.log('Annotation deleted:', annotation);

                // Remove annotation from localStorage
                removeAnnotationFromStorage(annotation.id);

                // Remove comment from localStorage
                let comments = loadCommentsFromStorage();
                comments = comments.filter(c => c.annotationId !== annotation.id);
                localStorage.setItem('annotorious-comments', JSON.stringify(comments));
                displayComments();
            });

            // Event: When annotation selection changes - HIGHLIGHT FEATURE
            anno.on('selectionChanged', annotations => {
                console.log('Selection changed:', annotations);

                // Remove all previous highlights
                removeAllHighlights();

                // Highlight comments for selected annotations
                if (annotations && annotations.length > 0) {
                    annotations.forEach(annotation => {
                        highlightComment(annotation.id);
                    });
                }
            });

            // Event: When annotation is clicked - HIGHLIGHT FEATURE
            anno.on('clickAnnotation', (annotation, event) => {
                console.log('Annotation clicked:', annotation);

                // Highlight the corresponding comment
                highlightComment(annotation.id);
            });

            // Event: When mouse enters annotation - SUBTLE HIGHLIGHT
            anno.on('mouseEnterAnnotation', (annotation, event) => {
                console.log('Mouse entered annotation:', annotation);

                // Add subtle highlight to corresponding comment
                const $commentItem = $(`.comment-item[data-annotation-id="${annotation.id}"]`);
                $commentItem.addClass('highlight-pulse');
            });

            // Event: When mouse leaves annotation - REMOVE SUBTLE HIGHLIGHT
            anno.on('mouseLeaveAnnotation', (annotation, event) => {
                console.log('Mouse left annotation:', annotation);

                // Remove subtle highlight from corresponding comment
                const $commentItem = $(`.comment-item[data-annotation-id="${annotation.id}"]`);
                $commentItem.removeClass('highlight-pulse');
            });

            // Add click event to comments to select and highlight corresponding annotation
            $(document).on('click', '.comment-item', function () {
                const annotationId = $(this).data('annotation-id');
                if (annotationId && window.annotoriusInstance) {
                    // Remove all previous highlights
                    removeAllHighlights();

                    // Highlight the annotation on the image
                    highlightAnnotationOnImage(annotationId);

                    // Highlight this comment
                    highlightComment(annotationId);
                }
            });
        });
    </script>
</body>

</html>