<!DOCTYPE html>
<html>

<head>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@annotorious/annotorious@latest/dist/annotorious.css">
    <script src="https://cdn.jsdelivr.net/npm/@annotorious/annotorious@latest/dist/annotorious.js"></script>
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
        }

        .container {
            display: flex;
            gap: 20px;
        }

        .image-container {
            width: 500px;
        }

        .comments-container {
            width: 300px;
            border: 1px solid #ccc;
            border-radius: 5px;
            padding: 15px;
            background-color: #f9f9f9;
        }

        .comments-header {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #333;
        }

        .comment-item {
            background: white;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 10px;
            margin-bottom: 10px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .comment-text {
            font-size: 14px;
            color: #555;
            margin-bottom: 5px;
        }

        .comment-meta {
            font-size: 12px;
            color: #888;
            border-top: 1px solid #eee;
            padding-top: 5px;
        }

        .no-comments {
            color: #999;
            font-style: italic;
            text-align: center;
            padding: 20px;
        }

        .clear-button {
            background-color: #ff4444;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin-top: 10px;
        }

        .clear-button:hover {
            background-color: #cc0000;
        }

        .comment-actions {
            margin-top: 8px;
            display: flex;
            gap: 5px;
        }

        .edit-btn,
        .delete-btn,
        .save-btn,
        .cancel-btn {
            padding: 4px 8px;
            border: none;
            border-radius: 3px;
            cursor: pointer;
            font-size: 12px;
        }

        .edit-btn {
            background-color: #007bff;
            color: white;
        }

        .edit-btn:hover {
            background-color: #0056b3;
        }

        .delete-btn {
            background-color: #dc3545;
            color: white;
        }

        .delete-btn:hover {
            background-color: #c82333;
        }

        .save-btn {
            background-color: #28a745;
            color: white;
        }

        .save-btn:hover {
            background-color: #218838;
        }

        .cancel-btn {
            background-color: #6c757d;
            color: white;
        }

        .cancel-btn:hover {
            background-color: #5a6268;
        }

        .comment-edit-input {
            width: 100%;
            padding: 5px;
            border: 1px solid #ddd;
            border-radius: 3px;
            font-size: 14px;
            margin-bottom: 5px;
            resize: vertical;
            min-height: 60px;
        }

        .my-comment {
            border-left: 4px solid #007bff;
            background-color: #f8f9ff;
        }

        .other-comment {
            border-left: 4px solid #6c757d;
            background-color: #f8f9fa;
        }

        .comment-owner-badge {
            display: inline-block;
            padding: 2px 6px;
            border-radius: 10px;
            font-size: 10px;
            font-weight: bold;
            margin-left: 5px;
        }

        .my-badge {
            background-color: #007bff;
            color: white;
        }

        .other-badge {
            background-color: #6c757d;
            color: white;
        }

        .user-selector {
            margin-bottom: 15px;
            padding: 10px;
            background-color: #e9ecef;
            border-radius: 5px;
        }

        .user-selector label {
            font-weight: bold;
            margin-right: 10px;
        }

        .user-selector select {
            padding: 5px;
            border: 1px solid #ccc;
            border-radius: 3px;
        }

        .filter-buttons {
            margin-bottom: 15px;
            display: flex;
            gap: 5px;
            flex-wrap: wrap;
        }

        .filter-btn {
            padding: 5px 10px;
            border: 1px solid #007bff;
            background-color: white;
            color: #007bff;
            border-radius: 3px;
            cursor: pointer;
            font-size: 12px;
        }

        .filter-btn.active {
            background-color: #007bff;
            color: white;
        }

        .filter-btn:hover {
            background-color: #0056b3;
            color: white;
        }

        .comment-highlighted {
            /* background-color: #fff3cd !important; */
            border: 2px solid #078bff !important;
            /* box-shadow: 0 4px 8px rgba(255, 193, 7, 0.3) !important; */
            /* transform: scale(1.02); */
            /* transition: all 0.3s ease; */
        }

        .comment-item {
            transition: all 0.3s ease;
        }

        /* .highlight-pulse {
            animation: pulse 1s ease-in-out;
        }
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        } */
    </style>
</head>

<body>
    <div class="container">
        <div class="image-container">
            <img id="my-image" src="https://images.pexels.com/photos/31241763/pexels-photo-31241763.jpeg"
                alt="Annotatable image" width="500px" />
        </div>

        <div class="comments-container">
            <div class="comments-header">Comments</div>

            <div class="user-selector">
                <label for="current-user">Current User:</label>
                <select id="current-user" onchange="changeCurrentUser()">
                    <option value="user-123">Sourabh Jingar</option>
                    <option value="user-456">John Doe</option>
                    <option value="user-789">Jane Smith</option>
                    <option value="user-101">Mike Johnson</option>
                </select>
            </div>

            <div class="filter-buttons">
                <button class="filter-btn active" onclick="filterComments('all')">All Comments</button>
                <button class="filter-btn" onclick="filterComments('mine')">My Comments</button>
                <button class="filter-btn" onclick="filterComments('others')">Others' Comments</button>
            </div>

            <div id="comments-list"></div>
            <button class="clear-button" onclick="clearAllComments()">Clear All Comments</button>
        </div>
    </div>
    <script>
        // Comment management functions
        function saveCommentToStorage(comment) {
            let comments = JSON.parse(localStorage.getItem('annotorious-comments') || '[]');
            comment.id = Date.now().toString(); // Simple ID generation
            comment.timestamp = new Date().toLocaleString();
            comment.userId = comment.userId || currentUserId; // Ensure userId is set
            comment.user = comment.user || currentUserName; // Ensure user name is set
            comments.push(comment);
            localStorage.setItem('annotorious-comments', JSON.stringify(comments));
            displayComments();
        }
        console.log(JSON.parse(localStorage.getItem('annotorious-comments')));

        function saveAnnotationToStorage(annotation) {
            let annotations = JSON.parse(localStorage.getItem('annotorious-annotations') || '[]');
            // Remove existing annotation with same ID if it exists
            annotations = annotations.filter(a => a.id !== annotation.id);
            annotations.push(annotation);
            localStorage.setItem('annotorious-annotations', JSON.stringify(annotations));
        }

        function loadAnnotationsFromStorage() {
            return JSON.parse(localStorage.getItem('annotorious-annotations') || '[]');
        }

        function removeAnnotationFromStorage(annotationId) {
            let annotations = loadAnnotationsFromStorage();
            annotations = annotations.filter(a => a.id !== annotationId);
            localStorage.setItem('annotorious-annotations', JSON.stringify(annotations));
        }

        function loadCommentsFromStorage() {
            return JSON.parse(localStorage.getItem('annotorious-comments') || '[]');
        }

        // Global variables for user management
        let currentFilter = 'all';
        let currentUserId = 'user-123';
        let currentUserName = 'Sourabh Jingar';

        // User data mapping
        const users = {
            'user-123': 'Sourabh Jingar',
            'user-456': 'John Doe',
            'user-789': 'Jane Smith',
            'user-101': 'Mike Johnson'
        };

        function getCurrentUser() {
            return {
                id: currentUserId,
                name: currentUserName
            };
        }

        function changeCurrentUser() {
            const select = $('#current-user');
            currentUserId = select.val();
            currentUserName = users[currentUserId];

            // Update the Annotorious user
            if (window.annotoriusInstance) {
                window.annotoriusInstance.setUser({
                    id: currentUserId,
                    name: currentUserName
                });
            }

            displayComments();
        }

        function filterComments(filterType) {
            currentFilter = filterType;

            // Update active button using jQuery
            $('.filter-btn').removeClass('active');
            $(event.target).addClass('active');

            displayComments();
        }

        // jQuery function to highlight comment
        function highlightComment(annotationId) {
            // Remove previous highlights
            $('.comment-item').removeClass('comment-highlighted highlight-pulse');

            // Find and highlight the comment
            const $commentItem = $(`.comment-item[data-annotation-id="${annotationId}"]`);
            if ($commentItem.length > 0) {
                $commentItem.addClass('comment-highlighted highlight-pulse');

                // Scroll to the comment if it's not visible
                $commentItem[0].scrollIntoView({
                    behavior: 'smooth',
                    block: 'center'
                });

                // Remove highlight after 3 seconds
                setTimeout(() => {
                    $commentItem.removeClass('comment-highlighted highlight-pulse');
                }, 3000);
            }
        }

        // jQuery function to remove all highlights
        function removeAllHighlights() {
            $('.comment-item').removeClass('comment-highlighted highlight-pulse');
        }

        function displayComments() {
            const commentsList = document.getElementById('comments-list');
            let comments = loadCommentsFromStorage();

            // Apply filter
            if (currentFilter === 'mine') {
                comments = comments.filter(comment => comment.userId === currentUserId);
            } else if (currentFilter === 'others') {
                comments = comments.filter(comment => comment.userId !== currentUserId);
            }

            if (comments.length === 0) {
                const message = currentFilter === 'mine' ? 'You have no comments yet.' :
                    currentFilter === 'others' ? 'No comments from other users.' :
                        'No comments yet. Create an annotation to add a comment!';
                commentsList.innerHTML = `<div class="no-comments">${message}</div>`;
                return;
            }

            commentsList.innerHTML = comments.map(comment => {
                const isMyComment = comment.userId === currentUserId;
                const commentClass = isMyComment ? 'my-comment' : 'other-comment';
                const badgeClass = isMyComment ? 'my-badge' : 'other-badge';
                const badgeText = isMyComment ? 'YOU' : 'OTHER';

                // Only show edit/delete buttons for user's own comments
                const actionButtons = isMyComment ? `
                    <button class="edit-btn" onclick="editComment('${comment.id}')">Edit</button>
                    <button class="delete-btn" onclick="deleteComment('${comment.id}', '${comment.annotationId}')">Delete</button>
                    <button class="save-btn" id="save-btn-${comment.id}" style="display: none;" onclick="saveComment('${comment.id}', '${comment.annotationId}')">Save</button>
                    <button class="cancel-btn" id="cancel-btn-${comment.id}" style="display: none;" onclick="cancelEdit('${comment.id}')">Cancel</button>
                ` : '';

                return `
                    <div class="comment-item ${commentClass}" data-comment-id="${comment.id}" data-annotation-id="${comment.annotationId}">
                        <div class="comment-text" id="comment-text-${comment.id}">${comment.text || 'No comment text'}</div>
                        <div class="comment-edit-container" id="edit-container-${comment.id}" style="display: none;">
                            <textarea class="comment-edit-input" id="edit-input-${comment.id}">${comment.text || ''}</textarea>
                        </div>
                        <div class="comment-meta">
                            <strong>By:</strong> ${comment.user || 'Anonymous'}
                            <span class="comment-owner-badge ${badgeClass}">${badgeText}</span><br>
                            <strong>Date:</strong> ${comment.timestamp}<br>
                        </div>
                        <div class="comment-actions">
                            ${actionButtons}
                        </div>
                    </div>
                `;
            }).join('');
        }

        function editComment(commentId) {
            const $textDiv = $(`#comment-text-${commentId}`);
            const $editContainer = $(`#edit-container-${commentId}`);
            const $editBtn = $(`.edit-btn[onclick="editComment('${commentId}')"]`);
            const $deleteBtn = $(`.delete-btn[onclick*="deleteComment('${commentId}"]`);
            const $saveBtn = $(`#save-btn-${commentId}`);
            const $cancelBtn = $(`#cancel-btn-${commentId}`);

            $textDiv.hide();
            $editContainer.show();
            $editBtn.hide();
            $deleteBtn.hide();
            $saveBtn.show();
            $cancelBtn.show();
        }

        function cancelEdit(commentId) {
            const $textDiv = $(`#comment-text-${commentId}`);
            const $editContainer = $(`#edit-container-${commentId}`);
            const $editBtn = $(`.edit-btn[onclick="editComment('${commentId}')"]`);
            const $deleteBtn = $(`.delete-btn[onclick*="deleteComment('${commentId}"]`);
            const $saveBtn = $(`#save-btn-${commentId}`);
            const $cancelBtn = $(`#cancel-btn-${commentId}`);

            $textDiv.show();
            $editContainer.hide();
            $editBtn.show();
            $deleteBtn.show();
            $saveBtn.hide();
            $cancelBtn.hide();
        }

        function saveComment(commentId, annotationId) {
            const newText = $(`#edit-input-${commentId}`).val().trim();

            if (!newText) {
                alert('Comment cannot be empty!');
                return;
            }

            // Update comment in localStorage
            let comments = loadCommentsFromStorage();
            const commentIndex = comments.findIndex(c => c.id === commentId);

            if (commentIndex !== -1) {
                comments[commentIndex].text = newText;
                comments[commentIndex].timestamp = new Date().toLocaleString();
                localStorage.setItem('annotorious-comments', JSON.stringify(comments));

                // Update the annotation in Annotorious
                updateAnnotationComment(annotationId, newText);

                displayComments();
            }
        }

        function deleteComment(commentId, annotationId) {
            if (confirm('Are you sure you want to delete this comment?')) {
                // Remove comment from localStorage
                let comments = loadCommentsFromStorage();
                comments = comments.filter(c => c.id !== commentId);
                localStorage.setItem('annotorious-comments', JSON.stringify(comments));

                // Delete the annotation from Annotorious
                deleteAnnotationFromMap(annotationId);

                displayComments();
            }
        }

        function clearAllComments() {
            if (confirm('Are you sure you want to clear all comments? This action cannot be undone.')) {
                localStorage.removeItem('annotorious-comments');
                localStorage.removeItem('annotorious-annotations');
                // Also clear all annotations from the map
                if (window.annotoriusInstance) {
                    const annotations = window.annotoriusInstance.getAnnotations();
                    annotations.forEach(annotation => {
                        window.annotoriusInstance.removeAnnotation(annotation);
                    });
                }
                displayComments();
            }
        }

        // Functions to interact with Annotorious annotations
        function updateAnnotationComment(annotationId, newText) {
            if (!window.annotoriusInstance) return;

            const annotation = window.annotoriusInstance.getAnnotations().find(a => a.id === annotationId);
            if (annotation) {
                // Create updated annotation with new comment
                const updatedAnnotation = {
                    ...annotation,
                    bodies: annotation.bodies.map(body => {
                        if (body.purpose === 'commenting') {
                            return {
                                ...body,
                                value: newText,
                                modified: new Date().toISOString()
                            };
                        }
                        return body;
                    })
                };

                // Update the annotation in Annotorious
                window.annotoriusInstance.updateAnnotation(updatedAnnotation, annotation);
            }
        }

        function deleteAnnotationFromMap(annotationId) {
            if (!window.annotoriusInstance) return;

            const annotation = window.annotoriusInstance.getAnnotations().find(a => a.id === annotationId);
            if (annotation) {
                window.annotoriusInstance.removeAnnotation(annotation);
                removeAnnotationFromStorage(annotationId);
            }
        }

        function loadExistingAnnotations() {
            if (!window.annotoriusInstance) return;

            const savedAnnotations = loadAnnotationsFromStorage();
            console.log('Loading saved annotations:', savedAnnotations);

            savedAnnotations.forEach(annotation => {
                try {
                    // Add the annotation to the map
                    window.annotoriusInstance.addAnnotation(annotation);
                    console.log('Restored annotation:', annotation.id);
                } catch (error) {
                    console.error('Error restoring annotation:', error, annotation);
                }
            });
        }

        // jQuery document ready function
        $(document).ready(function () {
            // Initialize comments display
            displayComments();

            const anno = Annotorious.createImageAnnotator('my-image', {
                widgets: [
                    // Enables the default COMMENT input box
                    { widget: 'COMMENT' }
                ],
                drawingEnabled: true
            });

            // Store the annotorious instance globally for access from other functions
            window.annotoriusInstance = anno;

            anno.setUser({
                id: currentUserId,
                name: currentUserName
            });

            // Load existing annotations from localStorage after a short delay
            setTimeout(() => {
                loadExistingAnnotations();
            }, 100);

            // Event: When annotation is created
            anno.on('createAnnotation', annotation => {
                console.log('Created annotation:', annotation);

                // Save the annotation to localStorage
                saveAnnotationToStorage(annotation);

                // Extract comment data and save to localStorage
                const commentText = annotation.bodies?.find(b => b.purpose === 'commenting')?.value || 'No comment';
                const commentData = {
                    text: commentText,
                    annotation: annotation,
                    user: currentUserName,
                    userId: currentUserId,
                    position: `x: ${Math.round(annotation.target.selector.geometry.bounds.minX)}, y: ${Math.round(annotation.target.selector.geometry.bounds.minY)}`,
                    annotationId: annotation.id
                };

                saveCommentToStorage(commentData);
            });

            // Event: When annotation is updated
            anno.on('updateAnnotation', (annotation, previous) => {
                console.log('Annotation updated:', annotation, previous);

                // Update the annotation in localStorage
                saveAnnotationToStorage(annotation);

                // Update comment in localStorage
                let comments = loadCommentsFromStorage();
                const commentIndex = comments.findIndex(c => c.annotationId === annotation.id);

                if (commentIndex !== -1) {
                    const commentText = annotation.bodies?.find(b => b.purpose === 'commenting')?.value || 'No comment';
                    comments[commentIndex].text = commentText;
                    comments[commentIndex].timestamp = new Date().toLocaleString();
                    localStorage.setItem('annotorious-comments', JSON.stringify(comments));
                    displayComments();
                }
            });

            // Event: When annotation is deleted
            anno.on('deleteAnnotation', annotation => {
                console.log('Annotation deleted:', annotation);

                // Remove annotation from localStorage
                removeAnnotationFromStorage(annotation.id);

                // Remove comment from localStorage
                let comments = loadCommentsFromStorage();
                comments = comments.filter(c => c.annotationId !== annotation.id);
                localStorage.setItem('annotorious-comments', JSON.stringify(comments));
                displayComments();
            });

            // Event: When annotation selection changes - HIGHLIGHT FEATURE
            anno.on('selectionChanged', annotations => {
                console.log('Selection changed:', annotations);

                // Remove all previous highlights
                removeAllHighlights();

                // Highlight comments for selected annotations
                if (annotations && annotations.length > 0) {
                    annotations.forEach(annotation => {
                        highlightComment(annotation.id);
                    });
                }
            });

            // Event: When annotation is clicked - HIGHLIGHT FEATURE
            anno.on('clickAnnotation', (annotation, event) => {
                console.log('Annotation clicked:', annotation);

                // Highlight the corresponding comment
                highlightComment(annotation.id);
            });

            // Event: When mouse enters annotation - SUBTLE HIGHLIGHT
            anno.on('mouseEnterAnnotation', (annotation, event) => {
                console.log('Mouse entered annotation:', annotation);

                // Add subtle highlight to corresponding comment
                const $commentItem = $(`.comment-item[data-annotation-id="${annotation.id}"]`);
                $commentItem.addClass('highlight-pulse');
            });

            // Event: When mouse leaves annotation - REMOVE SUBTLE HIGHLIGHT
            anno.on('mouseLeaveAnnotation', (annotation, event) => {
                console.log('Mouse left annotation:', annotation);

                // Remove subtle highlight from corresponding comment
                const $commentItem = $(`.comment-item[data-annotation-id="${annotation.id}"]`);
                $commentItem.removeClass('highlight-pulse');
            });

            // Add click event to comments to select corresponding annotation
            $(document).on('click', '.comment-item', function () {
                const annotationId = $(this).data('annotation-id');
                if (annotationId && window.annotoriusInstance) {
                    const annotation = window.annotoriusInstance.getAnnotations().find(a => a.id === annotationId);
                    if (annotation) {
                        // Select the annotation
                        window.annotoriusInstance.setSelected(annotation);

                        // Highlight this comment
                        highlightComment(annotationId);
                    }
                }
            });
        });
    </script>
</body>

</html>