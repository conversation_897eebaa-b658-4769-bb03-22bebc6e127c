<!DOCTYPE html>
<html>

<head>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@annotorious/annotorious@latest/dist/annotorious.css">
    <script src="https://cdn.jsdelivr.net/npm/@annotorious/annotorious@latest/dist/annotorious.js"></script>
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <style>
        .comment-highlighted {
            border: 2px solid #0d6efd !important;
            box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25) !important;
        }

        .comment-edit-input {
            resize: vertical;
            min-height: 60px;
        }
        .comment-item {
            transition: all 0.3s ease;
            cursor: pointer;
        }
        .comment-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15) !important;
        }
        .comment-meta {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 0.375rem;
            padding: 0.5rem;
            margin-top: 0.5rem;
        }
        .comment-text {
            font-size: 0.95rem;
            line-height: 1.5;
            color: #495057;
        }
        .comment-actions .btn {
            margin-right: 0.25rem;
            font-size: 0.75rem;
            padding: 0.25rem 0.5rem;
        }
        .user-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 0.8rem;
        }
        .comment-header {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            margin-bottom: 0.75rem;
        }
        .comment-user-info {
            flex: 1;
        }
        .comment-user-name {
            font-weight: 600;
            color: #495057;
            margin: 0;
            font-size: 0.9rem;
        }
        .comment-timestamp {
            color: #6c757d;
            font-size: 0.75rem;
            margin: 0;
        }
        .position-badge {
            background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
            color: white;
            font-size: 0.7rem;
            padding: 0.2rem 0.5rem;
            border-radius: 1rem;
            display: inline-block;
        }
        .comments-container .card {
            border: none;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .comments-container .card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
        }
        .no-comments-illustration {
            text-align: center;
            padding: 2rem 1rem;
            color: #6c757d;
        }
        .no-comments-illustration i {
            font-size: 3rem;
            margin-bottom: 1rem;
            opacity: 0.5;
        }
    </style>
</head>

<body>
    <div class="container-fluid p-3">
        <div class="row">
            <div class="col-md-8">
                <img id="my-image" src="https://images.pexels.com/photos/31241763/pexels-photo-31241763.jpeg"
                    alt="Annotatable image" class="img-fluid" />
            </div>
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-comments me-2"></i>
                            Comments
                            <span class="badge bg-light text-dark ms-2" id="comment-count">0</span>
                        </h5>
                    </div>
                    <div class="card-body p-2" style="max-height: 600px; overflow-y: auto;">
                        <div id="comments-list"></div>
                        <div class="d-grid gap-2 mt-3">
                            <button class="btn btn-outline-danger btn-sm" onclick="clearAllComments()">
                                <i class="fas fa-trash-alt me-2"></i>Clear All Comments
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script>
        // Comment management functions
        function saveCommentToStorage(comment) {
            let comments = JSON.parse(localStorage.getItem('annotorious-comments') || '[]');
            comment.id = Date.now().toString(); // Simple ID generation
            comment.timestamp = new Date().toLocaleString();
            comments.push(comment);
            localStorage.setItem('annotorious-comments', JSON.stringify(comments));
            displayComments();
        }
        console.log(JSON.parse(localStorage.getItem('annotorious-comments')));

        function saveAnnotationToStorage(annotation) {
            let annotations = JSON.parse(localStorage.getItem('annotorious-annotations') || '[]');
            // Remove existing annotation with same ID if it exists
            annotations = annotations.filter(a => a.id !== annotation.id);
            annotations.push(annotation);
            localStorage.setItem('annotorious-annotations', JSON.stringify(annotations));
        }

        function loadAnnotationsFromStorage() {
            return JSON.parse(localStorage.getItem('annotorious-annotations') || '[]');
        }

        function removeAnnotationFromStorage(annotationId) {
            let annotations = loadAnnotationsFromStorage();
            annotations = annotations.filter(a => a.id !== annotationId);
            localStorage.setItem('annotorious-annotations', JSON.stringify(annotations));
        }

        function loadCommentsFromStorage() {
            return JSON.parse(localStorage.getItem('annotorious-comments') || '[]');
        }



        // jQuery function to highlight comment
        function highlightComment(annotationId) {
            // Remove previous highlights
            $('.comment-item').removeClass('comment-highlighted highlight-pulse');

            // Find and highlight the comment
            const $commentItem = $(`.comment-item[data-annotation-id="${annotationId}"]`);
            if ($commentItem.length > 0) {
                $commentItem.addClass('comment-highlighted highlight-pulse');

                // Scroll to the comment if it's not visible
                $commentItem[0].scrollIntoView({
                    behavior: 'smooth',
                    block: 'center'
                });

                // Remove highlight after 3 seconds
                // setTimeout(() => {
                //     $commentItem.removeClass('comment-highlighted highlight-pulse');
                // }, 3000);
            }
        }

        // jQuery function to remove all highlights
        function removeAllHighlights() {
            $('.comment-item').removeClass('comment-highlighted highlight-pulse');
        }

        // Function to highlight annotation on the image
        function highlightAnnotationOnImage(annotationId) {
            if (!window.annotoriusInstance) return;

            const annotation = window.annotoriusInstance.getAnnotations().find(a => a.id === annotationId);
            if (annotation) {
                // First, clear any existing selections
                window.annotoriusInstance.clearSelection();

                // Select the annotation to highlight it
                window.annotoriusInstance.setSelected(annotation);

                // Add custom styling for enhanced visibility
                window.annotoriusInstance.setStyle((ann, state) => {
                    if (ann.id === annotationId && state.selected) {
                        return {
                            fill: '#ff6b6b',
                            fillOpacity: 0.3,
                            stroke: '#ff6b6b',
                            strokeWidth: 3,
                            strokeOpacity: 1
                        };
                    }
                    // Default style for other annotations
                    return {
                        fill: '#007bff',
                        fillOpacity: 0.2,
                        stroke: '#007bff',
                        strokeWidth: 2,
                        strokeOpacity: 0.8
                    };
                });

                console.log('Annotation highlighted on image:', annotationId);
            }
        }

        function displayComments() {
            const commentsList = document.getElementById('comments-list');
            let comments = loadCommentsFromStorage();

            if (comments.length === 0) {
                commentsList.innerHTML = `
                    <div class="no-comments-illustration">
                        <i class="fas fa-comments"></i>
                        <h6 class="mb-2">No Comments Yet</h6>
                        <p class="mb-0 small">Create an annotation to add your first comment!</p>
                    </div>
                `;
                return;
            }

            commentsList.innerHTML = comments.map(comment => {
                const userName = comment.user || 'Anonymous';
                const userInitial = userName.charAt(0).toUpperCase();
                const timeAgo = getTimeAgo(comment.timestamp);

                return `
                    <div class="card mb-3 comment-item" data-comment-id="${comment.id}" data-annotation-id="${comment.annotationId}">
                        <div class="card-body p-3">
                            <div class="comment-header">
                                <div class="user-avatar">${userInitial}</div>
                                <div class="comment-user-info">
                                    <p class="comment-user-name">${userName}</p>
                                    <p class="comment-timestamp">
                                        <i class="far fa-clock me-1"></i>${timeAgo}
                                    </p>
                                </div>
                                <div class="position-badge">
                                    <i class="fas fa-map-marker-alt me-1"></i>
                                    ${comment.position || 'Unknown'}
                                </div>
                            </div>

                            <div class="comment-content">
                                <div class="comment-text" id="comment-text-${comment.id}">
                                    <i class="fas fa-quote-left text-muted me-2"></i>
                                    ${comment.text || 'No comment text'}
                                </div>
                                <div class="comment-edit-container" id="edit-container-${comment.id}" style="display: none;">
                                    <textarea class="form-control comment-edit-input" id="edit-input-${comment.id}" placeholder="Edit your comment...">${comment.text || ''}</textarea>
                                </div>
                            </div>

                            <div class="comment-actions mt-3 d-flex gap-1">
                                <button class="btn btn-outline-primary btn-sm" onclick="editComment('${comment.id}')">
                                    <i class="fas fa-edit me-1"></i>Edit
                                </button>
                                <button class="btn btn-outline-danger btn-sm" onclick="deleteComment('${comment.id}', '${comment.annotationId}')">
                                    <i class="fas fa-trash me-1"></i>Delete
                                </button>
                                <button class="btn btn-success btn-sm" id="save-btn-${comment.id}" style="display: none;" onclick="saveComment('${comment.id}', '${comment.annotationId}')">
                                    <i class="fas fa-save me-1"></i>Save
                                </button>
                                <button class="btn btn-secondary btn-sm" id="cancel-btn-${comment.id}" style="display: none;" onclick="cancelEdit('${comment.id}')">
                                    <i class="fas fa-times me-1"></i>Cancel
                                </button>
                            </div>
                        </div>
                    </div>
                `;
            }).join('');
        }

        // Helper function to calculate time ago
        function getTimeAgo(timestamp) {
            if (!timestamp) return 'Unknown time';

            const now = new Date();
            const commentTime = new Date(timestamp);
            const diffInSeconds = Math.floor((now - commentTime) / 1000);

            if (diffInSeconds < 60) return 'Just now';
            if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`;
            if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`;
            if (diffInSeconds < 2592000) return `${Math.floor(diffInSeconds / 86400)}d ago`;

            return commentTime.toLocaleDateString();
        }

        // Helper function to update comment count
        function updateCommentCount(count) {
            const countBadge = document.getElementById('comment-count');
            if (countBadge) {
                countBadge.textContent = count;
                countBadge.className = count > 0 ? 'badge bg-primary ms-2' : 'badge bg-light text-dark ms-2';
            }
        }

        function editComment(commentId) {
            const $textDiv = $(`#comment-text-${commentId}`);
            const $editContainer = $(`#edit-container-${commentId}`);
            const $editBtn = $(`.edit-btn[onclick="editComment('${commentId}')"]`);
            const $deleteBtn = $(`.delete-btn[onclick*="deleteComment('${commentId}"]`);
            const $saveBtn = $(`#save-btn-${commentId}`);
            const $cancelBtn = $(`#cancel-btn-${commentId}`);

            $textDiv.hide();
            $editContainer.show();
            $editBtn.hide();
            $deleteBtn.hide();
            $saveBtn.show();
            $cancelBtn.show();
        }

        function cancelEdit(commentId) {
            const $textDiv = $(`#comment-text-${commentId}`);
            const $editContainer = $(`#edit-container-${commentId}`);
            const $editBtn = $(`.edit-btn[onclick="editComment('${commentId}')"]`);
            const $deleteBtn = $(`.delete-btn[onclick*="deleteComment('${commentId}"]`);
            const $saveBtn = $(`#save-btn-${commentId}`);
            const $cancelBtn = $(`#cancel-btn-${commentId}`);

            $textDiv.show();
            $editContainer.hide();
            $editBtn.show();
            $deleteBtn.show();
            $saveBtn.hide();
            $cancelBtn.hide();
        }

        function saveComment(commentId, annotationId) {
            const newText = $(`#edit-input-${commentId}`).val().trim();

            if (!newText) {
                alert('Comment cannot be empty!');
                return;
            }

            // Update comment in localStorage
            let comments = loadCommentsFromStorage();
            const commentIndex = comments.findIndex(c => c.id === commentId);

            if (commentIndex !== -1) {
                comments[commentIndex].text = newText;
                comments[commentIndex].timestamp = new Date().toLocaleString();
                localStorage.setItem('annotorious-comments', JSON.stringify(comments));

                // Update the annotation in Annotorious
                updateAnnotationComment(annotationId, newText);

                displayComments();
            }
        }

        function deleteComment(commentId, annotationId) {
            if (confirm('Are you sure you want to delete this comment?')) {
                // Remove comment from localStorage
                let comments = loadCommentsFromStorage();
                comments = comments.filter(c => c.id !== commentId);
                localStorage.setItem('annotorious-comments', JSON.stringify(comments));

                // Delete the annotation from Annotorious
                deleteAnnotationFromMap(annotationId);

                displayComments();
            }
        }

        function clearAllComments() {
            if (confirm('Are you sure you want to clear all comments? This action cannot be undone.')) {
                localStorage.removeItem('annotorious-comments');
                localStorage.removeItem('annotorious-annotations');
                // Also clear all annotations from the map
                if (window.annotoriusInstance) {
                    const annotations = window.annotoriusInstance.getAnnotations();
                    annotations.forEach(annotation => {
                        window.annotoriusInstance.removeAnnotation(annotation);
                    });
                }
                displayComments();
            }
        }

        // Functions to interact with Annotorious annotations
        function updateAnnotationComment(annotationId, newText) {
            if (!window.annotoriusInstance) return;

            const annotation = window.annotoriusInstance.getAnnotations().find(a => a.id === annotationId);
            if (annotation) {
                // Create updated annotation with new comment
                const updatedAnnotation = {
                    ...annotation,
                    bodies: annotation.bodies.map(body => {
                        if (body.purpose === 'commenting') {
                            return {
                                ...body,
                                value: newText,
                                modified: new Date().toISOString()
                            };
                        }
                        return body;
                    })
                };

                // Update the annotation in Annotorious
                window.annotoriusInstance.updateAnnotation(updatedAnnotation, annotation);
            }
        }

        function deleteAnnotationFromMap(annotationId) {
            if (!window.annotoriusInstance) return;

            const annotation = window.annotoriusInstance.getAnnotations().find(a => a.id === annotationId);
            if (annotation) {
                window.annotoriusInstance.removeAnnotation(annotation);
                removeAnnotationFromStorage(annotationId);
            }
        }

        function loadExistingAnnotations() {
            if (!window.annotoriusInstance) return;

            const savedAnnotations = loadAnnotationsFromStorage();
            console.log('Loading saved annotations:', savedAnnotations);

            savedAnnotations.forEach(annotation => {
                try {
                    // Add the annotation to the map
                    window.annotoriusInstance.addAnnotation(annotation);
                    // console.log('Restored annotation:', annotation.id);
                } catch (error) {
                    console.error('Error restoring annotation:', error, annotation);
                }
            });
        }

        // jQuery document ready function
        $(document).ready(function () {
            // Initialize comments display
            displayComments();

            const anno = Annotorious.createImageAnnotator('my-image', {
                widgets: [
                    // Enables the default COMMENT input box
                    { widget: 'COMMENT' }
                ],
                drawingEnabled: true
            });

            // Store the annotorious instance globally for access from other functions
            window.annotoriusInstance = anno;

            anno.setUser({
                id: 'user-123',
                name: 'sourabh'
            });

            // Load existing annotations from localStorage after a short delay
            setTimeout(() => {
                loadExistingAnnotations();
            }, 100);

            // Event: When annotation is created
            anno.on('createAnnotation', annotation => {
                console.log('Created annotation:', annotation);

                // Save the annotation to localStorage
                saveAnnotationToStorage(annotation);

                // Extract comment data and save to localStorage
                const commentText = annotation.bodies?.find(b => b.purpose === 'commenting')?.value || 'No comment';
                const commentData = {
                    text: commentText,
                    user: 'Anonymous User',
                    annotation: annotation,
                    annotationId: annotation.id

                };

                saveCommentToStorage(commentData);
            });

            // Event: When annotation is updated
            anno.on('updateAnnotation', (annotation, previous) => {
                console.log('Annotation updated:', annotation, previous);

                // Update the annotation in localStorage
                saveAnnotationToStorage(annotation);

                // Update comment in localStorage
                let comments = loadCommentsFromStorage();
                const commentIndex = comments.findIndex(c => c.annotationId === annotation.id);

                if (commentIndex !== -1) {
                    const commentText = annotation.bodies?.find(b => b.purpose === 'commenting')?.value || 'No comment';
                    comments[commentIndex].text = commentText;
                    comments[commentIndex].timestamp = new Date().toLocaleString();
                    localStorage.setItem('annotorious-comments', JSON.stringify(comments));
                    displayComments();
                }
            });

            // Event: When annotation is deleted
            anno.on('deleteAnnotation', annotation => {
                console.log('Annotation deleted:', annotation);

                // Remove annotation from localStorage
                removeAnnotationFromStorage(annotation.id);

                // Remove comment from localStorage
                let comments = loadCommentsFromStorage();
                comments = comments.filter(c => c.annotationId !== annotation.id);
                localStorage.setItem('annotorious-comments', JSON.stringify(comments));
                displayComments();
            });

            // Event: When annotation selection changes - HIGHLIGHT FEATURE
            anno.on('selectionChanged', annotations => {
                console.log('Selection changed:', annotations);

                // Remove all previous highlights
                removeAllHighlights();

                // Highlight comments for selected annotations
                if (annotations && annotations.length > 0) {
                    annotations.forEach(annotation => {
                        highlightComment(annotation.id);
                    });
                }
            });

            // Event: When annotation is clicked - HIGHLIGHT FEATURE
            anno.on('clickAnnotation', (annotation, event) => {
                console.log('Annotation clicked:', annotation);

                // Highlight the corresponding comment
                highlightComment(annotation.id);
            });

            // Event: When mouse enters annotation - SUBTLE HIGHLIGHT
            anno.on('mouseEnterAnnotation', (annotation, event) => {
                console.log('Mouse entered annotation:', annotation);

                // Add subtle highlight to corresponding comment
                const $commentItem = $(`.comment-item[data-annotation-id="${annotation.id}"]`);
                $commentItem.addClass('highlight-pulse');
            });

            // Event: When mouse leaves annotation - REMOVE SUBTLE HIGHLIGHT
            anno.on('mouseLeaveAnnotation', (annotation, event) => {
                console.log('Mouse left annotation:', annotation);

                // Remove subtle highlight from corresponding comment
                const $commentItem = $(`.comment-item[data-annotation-id="${annotation.id}"]`);
                $commentItem.removeClass('highlight-pulse');
            });

            // Add click event to comments to select and highlight corresponding annotation
            $(document).on('click', '.comment-item', function () {
                const annotationId = $(this).data('annotation-id');
                if (annotationId && window.annotoriusInstance) {
                    // Remove all previous highlights
                    removeAllHighlights();

                    // Highlight the annotation on the image
                    highlightAnnotationOnImage(annotationId);

                    // Highlight this comment
                    highlightComment(annotationId);
                }
            });
        });
    </script>
</body>

</html>