<!DOCTYPE html>
<html>

<head>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@annotorious/annotorious@latest/dist/annotorious.css">
    <script src="https://cdn.jsdelivr.net/npm/@annotorious/annotorious@latest/dist/annotorious.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
        }
        .container {
            display: flex;
            gap: 20px;
        }
        .image-container {
            width: 500px;
        }
        .comments-container {
            width: 300px;
            border: 1px solid #ccc;
            border-radius: 5px;
            padding: 15px;
            background-color: #f9f9f9;
        }
        .comments-header {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #333;
        }
        .comment-item {
            background: white;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 10px;
            margin-bottom: 10px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .comment-text {
            font-size: 14px;
            color: #555;
            margin-bottom: 5px;
        }
        .comment-meta {
            font-size: 12px;
            color: #888;
            border-top: 1px solid #eee;
            padding-top: 5px;
        }
        .no-comments {
            color: #999;
            font-style: italic;
            text-align: center;
            padding: 20px;
        }
        .clear-button {
            background-color: #ff4444;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin-top: 10px;
        }
        .clear-button:hover {
            background-color: #cc0000;
        }
    </style>
</head>

<body>
    <div class="container">
        <div class="image-container">
            <img id="my-image" src="https://images.pexels.com/photos/31241763/pexels-photo-31241763.jpeg"
                alt="Annotatable image" width="500px" />
        </div>

        <div class="comments-container">
            <div class="comments-header">Comments</div>
            <div id="comments-list"></div>
            <button class="clear-button" onclick="clearAllComments()">Clear All Comments</button>
        </div>
    </div>
    <script>
        // Comment management functions
        function saveCommentToStorage(comment) {
            let comments = JSON.parse(localStorage.getItem('annotorious-comments') || '[]');
            comment.id = Date.now().toString(); // Simple ID generation
            comment.timestamp = new Date().toLocaleString();
            comments.push(comment);
            localStorage.setItem('annotorious-comments', JSON.stringify(comments));
            displayComments();
        }

        function loadCommentsFromStorage() {
            return JSON.parse(localStorage.getItem('annotorious-comments') || '[]');
        }

        function displayComments() {
            const commentsList = document.getElementById('comments-list');
            const comments = loadCommentsFromStorage();

            if (comments.length === 0) {
                commentsList.innerHTML = '<div class="no-comments">No comments yet. Create an annotation to add a comment!</div>';
                return;
            }

            commentsList.innerHTML = comments.map(comment => `
                <div class="comment-item">
                    <div class="comment-text">${comment.text || 'No comment text'}</div>
                    <div class="comment-meta">
                        <strong>By:</strong> ${comment.user || 'Anonymous'}<br>
                        <strong>Date:</strong> ${comment.timestamp}<br>
                        <strong>Position:</strong> ${comment.position || 'Unknown'}
                    </div>
                </div>
            `).join('');
        }

        function clearAllComments() {
            if (confirm('Are you sure you want to clear all comments? This action cannot be undone.')) {
                localStorage.removeItem('annotorious-comments');
                displayComments();
            }
        }

        window.onload = function () {
            // Initialize comments display
            displayComments();

            const anno = Annotorious.createImageAnnotator('my-image', {
                widgets: [
                    // Enables the default COMMENT input box
                    { widget: 'COMMENT' }
                ],
                drawingEnabled: true
            });

            anno.setUser({
                id: 'user-123',
                name: 'Sourabh Jingar' // Replace with dynamic user name
            });

            anno.on('createAnnotation', annotation => {
                console.log('Created annotation:', annotation);

                // Extract comment data and save to localStorage
                const commentText = annotation.bodies?.find(b => b.purpose === 'commenting')?.value || 'No comment';
                const commentData = {
                    text: commentText,
                    user: annotation.bodies?.find(b => b.purpose === 'commenting')?.creator?.name || 'Sourabh Jingar',
                    position: `x: ${Math.round(annotation.target.selector.geometry.bounds.minX)}, y: ${Math.round(annotation.target.selector.geometry.bounds.minY)}`,
                    annotationId: annotation.id
                };

                saveCommentToStorage(commentData);
            });

            anno.on('updateAnnotation', (annotation, previous) => {
                console.log('Annotation updated:', annotation, previous);

                // Update comment in localStorage
                let comments = loadCommentsFromStorage();
                const commentIndex = comments.findIndex(c => c.annotationId === annotation.id);

                if (commentIndex !== -1) {
                    const commentText = annotation.bodies?.find(b => b.purpose === 'commenting')?.value || 'No comment';
                    comments[commentIndex].text = commentText;
                    comments[commentIndex].timestamp = new Date().toLocaleString();
                    localStorage.setItem('annotorious-comments', JSON.stringify(comments));
                    displayComments();
                }
            });

            anno.on('deleteAnnotation', annotation => {
                console.log('Annotation deleted:', annotation);

                // Remove comment from localStorage
                let comments = loadCommentsFromStorage();
                comments = comments.filter(c => c.annotationId !== annotation.id);
                localStorage.setItem('annotorious-comments', JSON.stringify(comments));
                displayComments();
            });
        };
    </script>
</body>

</html>